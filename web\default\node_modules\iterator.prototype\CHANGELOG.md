# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.5](https://github.com/ljharb/Iterator.prototype/compare/v1.1.4...v1.1.5) - 2025-01-01

### Commits

- [Refactor] use `get-proto` directly [`c36361a`](https://github.com/ljharb/Iterator.prototype/commit/c36361a6a83b128c054cc8ebed15a16436332255)
- [Deps] update `reflect.getprototypeof` [`c3f9c2a`](https://github.com/ljharb/Iterator.prototype/commit/c3f9c2a4aa0b84585e4990f465ca28771337c4b9)

## [v1.1.4](https://github.com/ljharb/Iterator.prototype/compare/v1.1.3...v1.1.4) - 2024-12-11

### Commits

- [actions] split out node 10-20, and 20+ [`a6e7fa2`](https://github.com/ljharb/Iterator.prototype/commit/a6e7fa21e5f470551594db6caf497ac3a1e1aa29)
- [Refactor] use `define-data-property` directly instead of `define-properties` [`9c55a02`](https://github.com/ljharb/Iterator.prototype/commit/9c55a029f606e751deb3bcbca4cff622acf806bc)
- [Deps] update `get-intrinsic`, `has-symbols`, `reflect.getprototypeof`, `set-function-name` [`958fdf5`](https://github.com/ljharb/Iterator.prototype/commit/958fdf52e5a331a2c35d7c70ca51272e7800c2f9)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `npmignore`, `tape` [`ab7c77d`](https://github.com/ljharb/Iterator.prototype/commit/ab7c77d7be8fe91b0a54b7e61eae85ba9c717bbe)
- [Robustness] use `es-object-atoms` [`ed8ee34`](https://github.com/ljharb/Iterator.prototype/commit/ed8ee3447ea912e6f915247d0245f59717ece94f)

## [v1.1.3](https://github.com/ljharb/Iterator.prototype/compare/v1.1.2...v1.1.3) - 2024-10-08

### Commits

- [Fix] in node 0.12 - 4, do not mutate Object.prototype [`4a4fa45`](https://github.com/ljharb/Iterator.prototype/commit/4a4fa458728ec2cb0c8e182956020989c7fb0573)
- [Tests] replace `aud` with `npm audit` [`ece6082`](https://github.com/ljharb/Iterator.prototype/commit/ece60822ffcd87c82db10add871cb9958c875e4f)
- [meta] add missing `engines.node` [`7ee3359`](https://github.com/ljharb/Iterator.prototype/commit/7ee335941706c5cebf940b541a73b4414fd47508)
- [Dev Deps] add missing peer dep [`fb90acc`](https://github.com/ljharb/Iterator.prototype/commit/fb90accee214ee8c40046edd6f90d6d0e983961a)

## [v1.1.2](https://github.com/ljharb/Iterator.prototype/compare/v1.1.1...v1.1.2) - 2023-09-13

### Commits

- [Fix] properly name `Iterator.prototype[Symbol.iterator]` method [`42ddaf7`](https://github.com/ljharb/Iterator.prototype/commit/42ddaf757d941ab3e5baf341ccb2598b8b86a1a1)
- [Deps] update `define-properties`, `reflect.getprototypeof` [`8b06aec`](https://github.com/ljharb/Iterator.prototype/commit/8b06aec1a6e79c14806a4ba1e783a2dc79de5e5d)

## [v1.1.1](https://github.com/ljharb/Iterator.prototype/compare/v1.1.0...v1.1.1) - 2023-08-30

### Commits

- [Fix] stop mutating node 4+ Iterator.prototype with Symbol.toStringTag [`74e9560`](https://github.com/ljharb/Iterator.prototype/commit/74e9560c10fcdadb207fea82577946976b9a87da)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`63fd269`](https://github.com/ljharb/Iterator.prototype/commit/63fd269da6a0a39f79bc48b887b7ec81b6e5e0a0)
- [Deps] update `define-properties`, `get-intrinsic` [`ef73f0e`](https://github.com/ljharb/Iterator.prototype/commit/ef73f0e78223e7eb09a987bc0614a802585e376a)

## [v1.1.0](https://github.com/ljharb/Iterator.prototype/compare/v1.0.1...v1.1.0) - 2022-12-09

### Commits

- [New] define `Symbol.toStringTag` [`d3f94aa`](https://github.com/ljharb/Iterator.prototype/commit/d3f94aaebf65eba391f702815216a32c5b6cbf18)

## [v1.0.1](https://github.com/ljharb/Iterator.prototype/compare/v1.0.0...v1.0.1) - 2022-12-09

### Commits

- [meta] fix repo URLs [`670823e`](https://github.com/ljharb/Iterator.prototype/commit/670823e0c547003a1006dcd0d27a22395a8dff1a)
- [Fix] move `has-symbols` to a runtime dep [`1b15df1`](https://github.com/ljharb/Iterator.prototype/commit/1b15df1dd481d9e12fbcf25f540b6ccfe9c51502)

## v1.0.0 - 2022-12-05

### Commits

- Initial implementation, tests, readme [`2db7663`](https://github.com/ljharb/Iterator.prototype/commit/2db76638655461506671b62ee97800288ae6d95b)
- Initial commit [`5c97994`](https://github.com/ljharb/Iterator.prototype/commit/5c979947a473848bf28d1bf286b813a6756a9700)
- npm init [`0c61e07`](https://github.com/ljharb/Iterator.prototype/commit/0c61e079e02b7604bfeaab3aa383171286bd4563)
- [meta] add `auto-changelog` [`8df6001`](https://github.com/ljharb/Iterator.prototype/commit/8df6001310793f8753fd776dc5db485e51624867)
- [meta] use `npmignore` to autogenerate an npmignore file [`d173afc`](https://github.com/ljharb/Iterator.prototype/commit/d173afcd7ef5bdfc1c5b9e90baf75885186a339e)
- Only apps should have lockfiles [`0fde03f`](https://github.com/ljharb/Iterator.prototype/commit/0fde03f141f5053fcba79a8bff09d410e25be201)
