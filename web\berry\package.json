{"name": "one_api_web", "version": "1.0.0", "proxy": "http://127.0.0.1:3000", "private": true, "homepage": "", "dependencies": {"@emotion/cache": "^11.9.3", "@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@mui/icons-material": "^5.8.4", "@mui/lab": "^5.0.0-alpha.88", "@mui/material": "^5.8.6", "@mui/system": "^5.8.6", "@mui/utils": "^5.8.6", "@mui/x-date-pickers": "^6.18.5", "@tabler/icons-react": "^2.44.0", "apexcharts": "3.35.3", "axios": "^0.27.2", "dayjs": "^1.11.10", "formik": "^2.2.9", "framer-motion": "^6.3.16", "history": "^5.3.0", "marked": "^4.1.1", "material-ui-popup-state": "^4.0.1", "notistack": "^3.0.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "1.4.0", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-perfect-scrollbar": "^1.5.8", "react-redux": "^8.0.2", "react-router": "6.3.0", "react-router-dom": "6.3.0", "react-scripts": "^5.0.1", "react-turnstile": "^1.1.2", "redux": "^4.2.0", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && mv -f build ../build/berry", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": ["defaults", "not IE 11"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "eslint": "^8.38.0", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "immutable": "^4.3.0", "prettier": "^2.8.7", "sass": "^1.53.0"}}